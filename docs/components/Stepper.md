# Stepper Component

Component Stepper cung cấp nhiều kiểu dáng và tùy chọn để tạo giao diện bước tiến trình trong ứng dụng với hỗ trợ đa ngôn ngữ, responsive và chuẩn theme.

## Tính năng

- **Nhiều kiểu dáng**: De<PERSON>ult, Minimal, Outlined, Filled, Dots, Arrows, Cards, Progress, Timeline, Numbered, Icons
- **Đa ngôn ngữ**: Hỗ trợ i18n với titleKey và descriptionKey
- **Responsive**: Tối ưu cho mobile và desktop
- **Dark theme**: Hỗ trợ dark theme với class `stepper-dark-theme`
- **Accessibility**: Hỗ trợ đầy đủ ARIA attributes
- **Animation**: Smooth transitions và loading states
- **Flexible orientation**: Horizontal và Vertical
- **Multiple color schemes**: Primary, Secondary, Success, Warning, Danger, Info

## Cách sử dụng cơ bản

```tsx
import { Stepper } from '@/shared/components/common';

const steps = [
  {
    id: 'step1',
    title: 'Thông tin cá nhân',
    titleKey: 'stepper.personal_info', // For i18n
    description: 'Nhập thông tin cá nhân',
    icon: 'user',
  },
  {
    id: 'step2',
    title: 'Xác thực',
    titleKey: 'stepper.verification',
    description: 'Xác thực tài khoản',
    icon: 'lock',
  },
  {
    id: 'step3',
    title: 'Hoàn thành',
    titleKey: 'stepper.complete',
    description: 'Hoàn tất đăng ký',
    icon: 'check',
  },
];

<Stepper
  steps={steps}
  currentStep={1}
  showStepIcons
  showNavigation
/>
```

## Props

### StepItem Interface

```tsx
interface StepItem {
  id: string | number;                    // ID duy nhất
  title: string;                          // Tiêu đề step
  titleKey?: string;                      // Key i18n cho title
  description?: string;                   // Mô tả step
  descriptionKey?: string;                // Key i18n cho description
  icon?: IconName;                        // Icon hiển thị
  status?: 'waiting' | 'processing' | 'completed' | 'error' | 'skipped';
  optional?: boolean;                     // Step tùy chọn
  disabled?: boolean;                     // Step bị vô hiệu hóa
  content?: React.ReactNode;              // Nội dung step
}
```

### StepperProps Interface

```tsx
interface StepperProps {
  steps: StepItem[];                      // Danh sách steps
  currentStep?: number;                   // Step hiện tại
  variant?: StepperVariant;               // Kiểu hiển thị
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'; // Kích thước
  orientation?: 'horizontal' | 'vertical'; // Hướng hiển thị
  colorScheme?: StepperColorScheme;       // Bảng màu
  showConnector?: boolean;                // Hiển thị đường nối
  showStepNumbers?: boolean;              // Hiển thị số thứ tự
  showStepIcons?: boolean;                // Hiển thị icon
  showStepContent?: boolean;              // Hiển thị nội dung step
  showNavigation?: boolean;               // Hiển thị navigation
  allowStepClick?: boolean;               // Cho phép click step
  responsive?: boolean;                   // Responsive design
  animated?: boolean;                     // Animation
  className?: string;                     // Class bổ sung
  onStepClick?: (stepId: string | number, stepIndex: number) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onComplete?: () => void;
}
```

## Các kiểu dáng (Variants)

### 1. Default
```tsx
<Stepper variant="default" steps={steps} />
```

### 2. Filled (Như trong ảnh)
```tsx
<div className="bg-slate-800 p-6 rounded-lg">
  <Stepper
    variant="filled"
    steps={steps}
    currentStep={1}
    showStepIcons
    colorScheme="danger"
    size="lg"
    className="stepper-dark-theme"
  />
</div>
```

### 3. Minimal
```tsx
<Stepper variant="minimal" steps={steps} />
```

### 4. Outlined
```tsx
<Stepper variant="outlined" steps={steps} />
```

### 5. Dots
```tsx
<Stepper variant="dots" steps={steps} />
```

### 6. Cards
```tsx
<Stepper variant="cards" steps={steps} />
```

### 7. Progress
```tsx
<Stepper variant="progress" steps={steps} />
```

### 8. Timeline
```tsx
<Stepper variant="timeline" orientation="vertical" steps={steps} />
```

### 9. Numbered
```tsx
<Stepper variant="numbered" steps={steps} />
```

### 10. Icons Only
```tsx
<Stepper variant="icons" showStepIcons steps={steps} />
```

## Color Schemes

```tsx
// Primary (default)
<Stepper colorScheme="primary" steps={steps} />

// Success
<Stepper colorScheme="success" steps={steps} />

// Warning  
<Stepper colorScheme="warning" steps={steps} />

// Danger
<Stepper colorScheme="danger" steps={steps} />

// Info
<Stepper colorScheme="info" steps={steps} />

// Secondary
<Stepper colorScheme="secondary" steps={steps} />
```

## Dark Theme Support

Để sử dụng trên nền tối, thêm class `stepper-dark-theme`:

```tsx
<div className="bg-slate-800 p-6 rounded-lg">
  <Stepper
    variant="filled"
    steps={steps}
    className="stepper-dark-theme"
    showStepIcons
  />
</div>
```

## Responsive Design

Component tự động responsive, có thể tùy chỉnh:

```tsx
<Stepper
  responsive={true}
  orientation="horizontal" // Sẽ chuyển thành vertical trên mobile
  steps={steps}
/>
```

## Đa ngôn ngữ (i18n)

```tsx
const steps = [
  {
    id: 'personal',
    title: 'Personal Info',
    titleKey: 'stepper.personal_info', // Sẽ dùng t('stepper.personal_info')
    description: 'Enter personal information',
    descriptionKey: 'stepper.personal_info_desc',
  },
  // ...
];

<Stepper steps={steps} />
```

## Ví dụ thực tế

### E-commerce Checkout
```tsx
const checkoutSteps = [
  { id: 'cart', title: 'Giỏ hàng', icon: 'shopping-cart' },
  { id: 'shipping', title: 'Thông tin giao hàng', icon: 'map-pin' },
  { id: 'payment', title: 'Thanh toán', icon: 'credit-card' },
  { id: 'confirmation', title: 'Xác nhận', icon: 'check' },
];

<Stepper
  variant="cards"
  colorScheme="success"
  steps={checkoutSteps}
  currentStep={2}
  showStepIcons
/>
```

### Form Wizard
```tsx
<Stepper
  steps={formSteps}
  currentStep={currentStep}
  showStepContent
  showNavigation
  onNext={() => setCurrentStep(prev => prev + 1)}
  onPrevious={() => setCurrentStep(prev => prev - 1)}
/>
```

### Project Timeline
```tsx
<Stepper
  variant="timeline"
  orientation="vertical"
  colorScheme="info"
  steps={projectSteps}
  showStepIcons
/>
```

## Accessibility

- Sử dụng đúng ARIA roles và attributes
- Hỗ trợ keyboard navigation
- Screen reader friendly
- Focus management
- High contrast support

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Demo

Xem demo đầy đủ tại: `/components/stepper`
