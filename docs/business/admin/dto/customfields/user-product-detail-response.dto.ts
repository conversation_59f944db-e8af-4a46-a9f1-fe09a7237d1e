import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { UserProductResponseDto } from './user-product-response.dto';
import { CustomFieldResponseDto, CustomFieldStatus } from './custom-field-response.dto';
import { CustomFieldValueDto } from './custom-field-value.dto';
import { ClassificationPriceDto } from './classification-price.dto';
import { CustomFieldConfigDto } from './custom-field-config.dto';

/**
 * DTO cho response trả về thông tin chi tiết trường tùy chỉnh với giá trị
 */
export class CustomFieldWithValueResponseDto extends CustomFieldResponseDto {
  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    nullable: true,
    type: () => CustomFieldValueDto
  })
  @IsOptional()
  @Type(() => CustomFieldValueDto)
  value: CustomFieldValueDto | null;

  // userId is already inherited from CustomFieldResponseDto
}

/**
 * DTO cho response trả về thông tin chi tiết nhóm trường tùy chỉnh
 */
export class CustomGroupFormResponseDto {
  @ApiProperty({
    description: 'ID của nhóm trường tùy chỉnh',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Thông tin chi tiết',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh với giá trị',
    type: [CustomFieldWithValueResponseDto],
  })
  @IsArray()
  @Type(() => CustomFieldWithValueResponseDto)
  fields: CustomFieldWithValueResponseDto[];
}

/**
 * DTO cho response trả về thông tin chi tiết của trường tùy chỉnh
 */
export class FieldDetailsResponseDto {
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Số sê-ri',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
  })
  @IsString()
  component: string;

  @ApiProperty({
    description: 'ID cấu hình',
    example: 'serial-number',
  })
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  @IsBoolean()
  required: boolean;

  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  employeeId: number | null;

  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  userId: number | null;
}

/**
 * DTO cho response trả về thông tin giá trị trường tùy chỉnh với chi tiết trường
 * Sử dụng cấu trúc giống với CustomFieldWithValueResponseDto để đồng nhất
 */
export class CustomFieldValueWithDetailsResponseDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 8,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
  })
  @IsString()
  component: string;

  @ApiProperty({
    description: 'ID cấu hình',
    example: 'serial-number',
  })
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Số sê-ri',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  @IsBoolean()
  required: boolean;

  @ApiProperty({
    description: 'Cấu hình chi tiết',
    type: () => CustomFieldConfigDto
  })
  @Type(() => CustomFieldConfigDto)
  configJson: CustomFieldConfigDto;

  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: null,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  employeeId: number | null;

  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  userId: number | null;

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
  })
  @IsNumber()
  createAt: number;

  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    type: () => CustomFieldValueDto
  })
  @Type(() => CustomFieldValueDto)
  value: CustomFieldValueDto;
}

/**
 * DTO cho response trả về thông tin phân loại sản phẩm
 */
export class ClassificationResponseDto {
  @ApiProperty({
    description: 'ID của phân loại',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Loại phân loại',
    example: 'Màu sắc',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Giá của phân loại',
    nullable: true,
    type: () => ClassificationPriceDto
  })
  @IsOptional()
  @Type(() => ClassificationPriceDto)
  price: ClassificationPriceDto | null;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh với giá trị',
    type: [CustomFieldValueWithDetailsResponseDto],
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  @Type(() => CustomFieldValueWithDetailsResponseDto)
  customFields: CustomFieldValueWithDetailsResponseDto[] | null;
}

/**
 * DTO cho response trả về chi tiết sản phẩm của người dùng
 * Mở rộng từ UserProductResponseDto và thêm thông tin nhóm trường tùy chỉnh và phân loại
 */
export class UserProductDetailResponseDto extends UserProductResponseDto {
  @ApiProperty({
    description: 'Danh sách nhóm trường tùy chỉnh',
    type: [CustomGroupFormResponseDto],
    nullable: false,
  })
  @IsArray()
  @Type(() => CustomGroupFormResponseDto)
  customGroupForms: CustomGroupFormResponseDto[];

  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [ClassificationResponseDto],
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  @Type(() => ClassificationResponseDto)
  classifications: ClassificationResponseDto[] | null;
}
