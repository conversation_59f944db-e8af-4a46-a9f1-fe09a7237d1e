import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CustomFieldConfigDto } from './custom-field-config.dto';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * Enum cho trạng thái của trường tùy chỉnh
 */
export enum CustomFieldStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DELETED = 'DELETED'
}

/**
 * DTO cho response trả về thông tin trường tùy chỉnh
 */
export class CustomFieldResponseDto {
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  @IsNumber()
  id: number;



  @ApiProperty({
    description: 'ID cấu hình',
    example: 'product_color',
  })
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Màu sắc',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  @IsBoolean()
  required: boolean;

  @ApiProperty({
    description: 'Cấu hình chi tiết',
    type: () => CustomFieldConfigDto
  })
  @Type(() => CustomFieldConfigDto)
  configJson: CustomFieldConfigDto;

  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  employeeId: number | null;

  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1,
    nullable: true,
  })
  @IsOptional()
  @IsNumber()
  userId: number | null;

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
  })
  @IsNumber()
  createAt: number;

  @ApiProperty({
    description: 'Trạng thái của trường tùy chỉnh',
    example: CustomFieldStatus.APPROVED,
    enum: CustomFieldStatus,
  })
  @IsString()
  status: CustomFieldStatus;
}
