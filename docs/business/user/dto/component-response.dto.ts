import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho cấu hình của thành phần
 */
export class ComponentConfigDto {
  @Expose()
  @ApiProperty({
    description: 'ID của cấu hình',
    example: 'text-input',
  })
  id: string;

  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Họ và tên',
  })
  label: string;

  @Expose()
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  type: string;

  @Expose()
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  required: boolean;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình validation',
    example: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z0-9 ]*$' },
    required: false,
  })
  validation?: any;

  @Expose()
  @ApiProperty({
    description: 'Placeholder',
    example: '<PERSON>hập họ và tên',
    required: false,
  })
  placeholder?: string;

  @Expose()
  @ApiProperty({
    description: 'Variant',
    example: 'outlined',
    required: false,
  })
  variant?: string;

  @Expose()
  @ApiProperty({
    description: 'Kích thước',
    example: 'small',
    required: false,
  })
  size?: string;

  @Expose()
  @ApiProperty({
    description: 'Danh sách tùy chọn',
    example: ['Việt Nam', 'Mỹ', 'Nhật Bản'],
    required: false,
  })
  options?: string[];
}

/**
 * DTO cho thành phần
 */
export class ComponentItemDto {
  @Expose()
  @ApiProperty({
    description: 'Tên thành phần',
    example: 'Text Input',
  })
  component: string;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình của thành phần',
    type: ComponentConfigDto,
  })
  @Type(() => ComponentConfigDto)
  config: ComponentConfigDto;
}

/**
 * DTO cho danh sách thành phần
 */
export class ComponentListResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Danh sách thành phần',
    type: [ComponentItemDto],
  })
  @Type(() => ComponentItemDto)
  data: ComponentItemDto[];

  @Expose()
  @ApiProperty({
    description: 'Tổng số thành phần',
    example: 2,
  })
  total: number;
}
