import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

/**
 * DTO cho cấu hình trường tùy chỉnh
 */
export class FieldConfigDto {
  /**
   * ID cấu hình (bắt buộc nhập)
   * @example "text-input"
   */
  @ApiProperty({
    description: 'ID cấu hình (bắt buộc nhập)',
    example: 'text-input',
    required: true
  })
  @IsNotEmpty({ message: 'ID cấu hình không được để trống' })
  @IsString({ message: 'ID cấu hình phải là chuỗi' })
  id: string;

  /**
   * Nhãn hiển thị
   * @example "Họ và tên"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Họ và tên',
  })
  @IsNotEmpty({ message: 'Nhãn hiển thị không được để trống' })
  @IsString({ message: 'Nhãn hiển thị phải là chuỗi' })
  label: string;

  /**
   * Loại trường
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsNotEmpty({ message: 'Loại trường không được để trống' })
  @IsString({ message: 'Loại trường phải là chuỗi' })
  type: string;

  /**
   * Trường bắt buộc hay không
   * @example true
   */
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  required: boolean;

  /**
   * Cấu hình validation
   * @example { "minLength": 3, "maxLength": 50, "pattern": "^[a-zA-Z\\s]*$" }
   */
  @ApiProperty({
    description: 'Cấu hình validation',
    example: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z\\s]*$' },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình validation phải là đối tượng' })
  validation?: any;

  /**
   * Giá trị mặc định
   * @example ""
   */
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: '',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Giá trị mặc định phải là chuỗi' })
  defaultValue?: string;

  /**
   * Placeholder
   * @example "Nhập họ và tên"
   */
  @ApiProperty({
    description: 'Placeholder',
    example: 'Nhập họ và tên',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Placeholder phải là chuỗi' })
  placeholder?: string;

  /**
   * Các thuộc tính bổ sung
   */
  @ApiProperty({
    description: 'Các thuộc tính bổ sung',
    example: {
      variant: 'outlined',
      size: 'small',
      fullWidth: true
    },
    required: false,
    additionalProperties: true
  })
  @IsOptional()
  @IsObject({ message: 'Các thuộc tính bổ sung phải là đối tượng' })
  additionalProperties?: Record<string, any>;

  // Cho phép các thuộc tính khác
  [key: string]: any;
}

/**
 * DTO cho tạo trường tùy chỉnh (chỉ dùng cho Swagger)
 */
export class CreateCustomFieldSwaggerDto {

  /**
   * Cấu hình trường
   */
  @ApiProperty({
    description: 'Cấu hình trường',
    type: FieldConfigDto,
    example: {
      id: 'text-input-001',
      label: 'Họ và tên',
      type: 'text',
      required: true,
      validation: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z\\s]*$' },
      defaultValue: '',
      placeholder: 'Nhập họ và tên',
      variant: 'outlined',
      size: 'small'
    }
  })
  @IsNotEmpty({ message: 'Cấu hình trường không được để trống' })
  @IsObject({ message: 'Cấu hình trường phải là đối tượng' })
  config: FieldConfigDto;

  /**
   * ID nhóm trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID nhóm trường tùy chỉnh',
    example: 1,
    required: false,
  })
  @IsOptional()
  formGroupId?: number;

  /**
   * Cấu hình grid
   * @example { "i": "field1", "x": 0, "y": 0, "w": 6, "h": 2 }
   */
  @IsOptional()
  @IsObject({ message: 'Cấu hình grid phải là đối tượng' })
  grid?: any;
}
