import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as request from 'supertest';
import { BusinessUserModule } from '../../business-user.module';
import { PhysicalWarehouse, Warehouse } from '@modules/business/entities';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { JwtService } from '@nestjs/jwt';

describe('Bulk Delete Physical Warehouse Integration', () => {
  let app: INestApplication;
  let jwtService: JwtService;
  let authToken: string;

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [PhysicalWarehouse, Warehouse],
          synchronize: true,
        }),
        BusinessUserModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);
    
    // Tạo JWT token cho test
    authToken = jwtService.sign(mockUser);
    
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('DELETE /user/physical-warehouses/bulk', () => {
    it('should successfully delete multiple physical warehouses', async () => {
      // Arrange - Tạo test data
      const warehouseIds = [1, 2, 3];
      
      // Giả lập việc tạo warehouses và physical warehouses
      // Trong test thực tế, bạn sẽ cần tạo dữ liệu test trong database

      const requestBody = {
        warehouseIds: warehouseIds,
      };

      // Act & Assert
      const response = await request(app.getHttpServer())
        .delete('/user/physical-warehouses/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send(requestBody)
        .expect((res) => {
          // Kiểm tra structure của response
          expect(res.body).toHaveProperty('success');
          expect(res.body).toHaveProperty('data');
          expect(res.body.data).toHaveProperty('totalRequested');
          expect(res.body.data).toHaveProperty('successCount');
          expect(res.body.data).toHaveProperty('failureCount');
          expect(res.body.data).toHaveProperty('results');
          expect(res.body.data).toHaveProperty('message');
        });

      // Kiểm tra chi tiết response
      expect(response.body.data.totalRequested).toBe(warehouseIds.length);
      expect(Array.isArray(response.body.data.results)).toBe(true);
      expect(response.body.data.results).toHaveLength(warehouseIds.length);

      // Kiểm tra từng result item
      response.body.data.results.forEach((result: any, index: number) => {
        expect(result).toHaveProperty('warehouseId');
        expect(result).toHaveProperty('status');
        expect(result).toHaveProperty('message');
        expect(result.warehouseId).toBe(warehouseIds[index]);
        expect(['success', 'error']).toContain(result.status);
      });
    });

    it('should return 400 for invalid request body', async () => {
      const invalidRequestBody = {
        warehouseIds: [], // Empty array should fail validation
      };

      await request(app.getHttpServer())
        .delete('/user/physical-warehouses/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidRequestBody)
        .expect(400);
    });

    it('should return 401 for missing authorization', async () => {
      const requestBody = {
        warehouseIds: [1, 2, 3],
      };

      await request(app.getHttpServer())
        .delete('/user/physical-warehouses/bulk')
        .send(requestBody)
        .expect(401);
    });

    it('should handle partial success scenario', async () => {
      // Arrange - Mix of valid and invalid warehouse IDs
      const requestBody = {
        warehouseIds: [1, 999, 2], // 999 should not exist
      };

      const response = await request(app.getHttpServer())
        .delete('/user/physical-warehouses/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send(requestBody);

      // Should return 207 Multi-Status if some operations fail
      expect([200, 207]).toContain(response.status);
      
      if (response.status === 207) {
        expect(response.body.data.failureCount).toBeGreaterThan(0);
        expect(response.body.data.successCount).toBeGreaterThanOrEqual(0);
      }
    });

    it('should validate warehouse IDs are numbers', async () => {
      const invalidRequestBody = {
        warehouseIds: ['invalid', 'ids'], // Should be numbers
      };

      await request(app.getHttpServer())
        .delete('/user/physical-warehouses/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidRequestBody)
        .expect(400);
    });

    it('should validate unique warehouse IDs', async () => {
      const invalidRequestBody = {
        warehouseIds: [1, 1, 2], // Duplicate IDs should fail
      };

      await request(app.getHttpServer())
        .delete('/user/physical-warehouses/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidRequestBody)
        .expect(400);
    });
  });
});
