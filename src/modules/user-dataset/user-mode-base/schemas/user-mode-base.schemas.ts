import { z } from 'zod';
import {
  ModeBaseProvider,
  ModeBaseStatus,
  ModeBaseType,
  UserModeBaseSortBy,
} from '../types/user-mode-base.types';

/**
 * Schema cho việc truy vấn danh sách mode base
 */
export const UserModeBaseQuerySchema = z.object({
  page: z
    .number()
    .int()
    .min(1, 'Số trang phải lớn hơn 0')
    .optional()
    .default(1),
  
  limit: z
    .number()
    .int()
    .min(1, 'Số lượng item phải lớn hơn 0')
    .max(100, 'Số lượng item không được vượt quá 100')
    .optional()
    .default(10),
  
  search: z
    .string()
    .optional(),
  
  provider: z
    .nativeEnum(ModeBaseProvider, {
      errorMap: () => ({ message: 'Nhà cung cấp không hợp lệ' }),
    })
    .optional(),
  
  type: z
    .nativeEnum(ModeBaseType, {
      errorMap: () => ({ message: 'Loại mode không hợp lệ' }),
    })
    .optional(),
  
  status: z
    .nativeEnum(ModeBaseStatus, {
      errorMap: () => ({ message: 'Trạng thái không hợp lệ' }),
    })
    .optional(),
  
  supportsFineTuning: z
    .boolean()
    .optional(),
  
  supportsFunctionCalling: z
    .boolean()
    .optional(),
  
  supportsVision: z
    .boolean()
    .optional(),
  
  sortBy: z
    .nativeEnum(UserModeBaseSortBy, {
      errorMap: () => ({ message: 'Trường sắp xếp không hợp lệ' }),
    })
    .optional()
    .default(UserModeBaseSortBy.NAME),
  
  sortDirection: z
    .enum(['ASC', 'DESC'], {
      errorMap: () => ({ message: 'Hướng sắp xếp không hợp lệ' }),
    })
    .optional()
    .default('ASC'),
});

/**
 * Schema cho việc so sánh modes
 */
export const CompareModesSchema = z.object({
  modeIds: z
    .array(z.string().uuid('ID mode không hợp lệ'))
    .min(2, 'Cần ít nhất 2 modes để so sánh')
    .max(5, 'Chỉ có thể so sánh tối đa 5 modes'),
});

/**
 * Schema cho việc đánh giá mode
 */
export const RateModeSchema = z.object({
  rating: z
    .number()
    .int()
    .min(1, 'Điểm đánh giá phải từ 1 đến 5')
    .max(5, 'Điểm đánh giá phải từ 1 đến 5'),
  
  comment: z
    .string()
    .max(1000, 'Nhận xét không được vượt quá 1000 ký tự')
    .optional(),
  
  useCase: z
    .string()
    .max(200, 'Use case không được vượt quá 200 ký tự')
    .optional(),
});

/**
 * Schema cho response của mode base
 */
export const UserModeBaseResponseSchema = z.object({
  id: z.string().uuid('ID không hợp lệ'),
  name: z.string(),
  displayName: z.string(),
  description: z.string(),
  provider: z.nativeEnum(ModeBaseProvider),
  type: z.nativeEnum(ModeBaseType),
  status: z.nativeEnum(ModeBaseStatus),
  maxTokens: z.number().int().min(1),
  costPerInputToken: z.number().min(0),
  costPerOutputToken: z.number().min(0),
  currency: z.string(),
  supportsFineTuning: z.boolean(),
  supportsFunctionCalling: z.boolean(),
  supportsVision: z.boolean(),
  createdAt: z.number(),
  updatedAt: z.number(),
  usageCount: z.number().int().min(0),
  averageRating: z.number().min(0).max(5),
});

/**
 * Schema cho response chi tiết của mode base
 */
export const UserModeBaseDetailResponseSchema = UserModeBaseResponseSchema.extend({
  specifications: z.object({
    architecture: z.string(),
    trainingData: z.string(),
    languages: z.array(z.string()),
    capabilities: z.array(z.string()),
    limitations: z.array(z.string()),
  }),
  
  defaultConfig: z.object({
    temperature: z.number().min(0).max(2),
    maxTokens: z.number().int().min(1),
    topP: z.number().min(0).max(1),
    frequencyPenalty: z.number().min(-2).max(2),
    presencePenalty: z.number().min(-2).max(2),
  }),
  
  examples: z.array(z.object({
    title: z.string(),
    description: z.string(),
    input: z.string(),
    output: z.string(),
    config: z.record(z.unknown()).optional(),
  })),
  
  performanceStats: z.object({
    averageResponseTime: z.number().min(0),
    successRate: z.number().min(0).max(100),
    errorRate: z.number().min(0).max(100),
    throughput: z.number().min(0),
  }),
  
  changelog: z.array(z.object({
    version: z.string(),
    date: z.string(),
    changes: z.array(z.string()),
  })),
});

/**
 * Schema cho response so sánh modes
 */
export const CompareModesResponseSchema = z.object({
  modes: z.array(UserModeBaseDetailResponseSchema),
  
  comparison: z.object({
    features: z.array(z.object({
      name: z.string(),
      values: z.array(z.object({
        modeId: z.string().uuid(),
        value: z.union([z.string(), z.number(), z.boolean()]),
        score: z.number().optional(),
      })),
    })),
    
    performance: z.array(z.object({
      metric: z.string(),
      values: z.array(z.object({
        modeId: z.string().uuid(),
        value: z.number(),
        unit: z.string(),
      })),
    })),
    
    cost: z.array(z.object({
      scenario: z.string(),
      values: z.array(z.object({
        modeId: z.string().uuid(),
        cost: z.number(),
        currency: z.string(),
      })),
    })),
  }),
  
  recommendations: z.array(z.object({
    useCase: z.string(),
    recommendedModeId: z.string().uuid(),
    reason: z.string(),
    score: z.number().min(0).max(100),
  })),
});

/**
 * Schema cho response đánh giá mode
 */
export const RateModeResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  newAverageRating: z.number().min(0).max(5),
  totalRatings: z.number().int().min(0),
});

/**
 * Schema cho paginated result
 */
export const PaginatedResultSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    total: z.number().int().min(0),
    page: z.number().int().min(1),
    limit: z.number().int().min(1),
    totalPages: z.number().int().min(0),
  });

// Export types inferred from schemas
export type UserModeBaseQueryFormData = z.infer<typeof UserModeBaseQuerySchema>;
export type CompareModesFormData = z.infer<typeof CompareModesSchema>;
export type RateModeFormData = z.infer<typeof RateModeSchema>;
