import React, { useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Plus,
  Target,
  Play,
  TrendingUp,
  DollarSign
} from 'lucide-react';
import {
  Card,
  Table,
  Chip,
  IconCard,
  Tooltip,
  Button,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useZaloAdsCampaigns } from '../../hooks/zalo-ads/useZaloAdsCampaigns';
import { CreateZaloAdsCampaignForm } from '../../components/zalo-ads/CreateZaloAdsCampaignForm';
import type { ZaloAdsCampaignQueryDto, ZaloAdsCampaignDto, ZaloAdsCampaignStatus, ZaloAdsObjective } from '../../types/zalo-ads.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý chiến dịch Zalo Ads
 */
export function ZaloAdsCampaignsPage() {
  const { t } = useTranslation('marketing');
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedCampaign, setSelectedCampaign] = React.useState<ZaloAdsCampaignDto | null>(null);

  // Sử dụng hook animation cho form
  const { isVisible: isCreateVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();
  const { isVisible: isEditVisible, showForm: showEditForm, hideForm: hideEditForm } = useSlideForm();

  // Kiểm tra nếu có action=create trong URL thì hiển thị form
  useEffect(() => {
    if (searchParams.get('action') === 'create') {
      showCreateForm();
      setSearchParams({});
    }
  }, [searchParams, showCreateForm, setSearchParams]);

  const handleEditCampaign = useCallback((campaign: ZaloAdsCampaignDto) => {
    setSelectedCampaign(campaign);
    showEditForm();
  }, [showEditForm]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<ZaloAdsCampaignDto>[]>(
    () => [
      {
        key: 'campaign',
        title: t('marketing:zaloAds.campaigns.table.campaign', 'Chiến dịch'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: ZaloAdsCampaignDto) => (
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-white">
              <Target className="h-5 w-5" />
            </div>
            <div>
              <div className="font-medium">{String(value || '')}</div>
              <div className="text-sm text-muted-foreground">
                ID: {record.campaignId}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: 'objective',
        title: t('marketing:zaloAds.campaigns.table.objective', 'Mục tiêu'),
        dataIndex: 'objective',
        sortable: true,
        render: (value: unknown) => {
          const objective = value as ZaloAdsObjective;
          return (
            <Chip variant="info">
              {objective}
            </Chip>
          );
        },
      },
      {
        key: 'status',
        title: t('marketing:zaloAds.campaigns.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as ZaloAdsCampaignStatus;
          switch (status) {
            case 'ACTIVE':
              return (
                <Chip variant="success" leftIconName="play">
                  {t('marketing:zaloAds.campaigns.status.active', 'Đang chạy')}
                </Chip>
              );
            case 'PAUSED':
              return (
                <Chip variant="warning" leftIconName="pause">
                  {t('marketing:zaloAds.campaigns.status.paused', 'Tạm dừng')}
                </Chip>
              );
            case 'DRAFT':
              return (
                <Chip variant="info" leftIconName="edit">
                  {t('marketing:zaloAds.campaigns.status.draft', 'Bản nháp')}
                </Chip>
              );
            case 'PENDING_REVIEW':
              return (
                <Chip variant="warning" leftIconName="clock">
                  {t('marketing:zaloAds.campaigns.status.pending', 'Chờ duyệt')}
                </Chip>
              );
            case 'REJECTED':
              return (
                <Chip variant="danger" leftIconName="x">
                  {t('marketing:zaloAds.campaigns.status.rejected', 'Từ chối')}
                </Chip>
              );
            default:
              return <Chip variant="info">{status}</Chip>;
          }
        },
      },
      {
        key: 'budget',
        title: t('marketing:zaloAds.campaigns.table.budget', 'Ngân sách'),
        dataIndex: 'dailyBudget',
        sortable: true,
        render: (value: unknown, record: ZaloAdsCampaignDto) => (
          <div className="text-right">
            <div className="font-medium">
              {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
                maximumFractionDigits: 0
              }).format(Number(value || record.lifetimeBudget || 0))}
            </div>
            <div className="text-xs text-muted-foreground">
              {value ? 'Hàng ngày' : 'Tổng'}
            </div>
          </div>
        ),
      },
      {
        key: 'bidStrategy',
        title: t('marketing:zaloAds.campaigns.table.bidStrategy', 'Đấu giá'),
        dataIndex: 'bidStrategy',
        render: (value: unknown) => (
          <span className="text-sm">
            {String(value || '')}
          </span>
        ),
      },
      {
        key: 'placements',
        title: t('marketing:zaloAds.campaigns.table.placements', 'Vị trí'),
        dataIndex: 'placements',
        render: (value: unknown) => {
          const placements = value as string[];
          return (
            <div className="flex flex-wrap gap-1">
              {placements?.slice(0, 2).map((placement: string) => (
                <Chip key={placement} variant="info">
                  {placement}
                </Chip>
              ))}
              {placements?.length > 2 && (
                <Chip variant="info">
                  +{placements.length - 2}
                </Chip>
              )}
            </div>
          );
        },
      },
      {
        key: 'updatedAt',
        title: t('marketing:zaloAds.campaigns.table.updated', 'Cập nhật'),
        dataIndex: 'updatedAt',
        sortable: true,
        render: (value: unknown) => (
          <span className="text-sm text-muted-foreground">
            {value ? new Date(Number(value)).toLocaleDateString('vi-VN') : ''}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('marketing:zaloAds.campaigns.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: ZaloAdsCampaignDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('marketing:zaloAds.campaigns.viewPerformance', 'Xem hiệu suất')}>
              <IconCard
                icon="bar-chart"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('View performance:', record.id);
                }}
              />
            </Tooltip>
            <Tooltip content={t('common.edit', 'Chỉnh sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => handleEditCampaign(record)}
              />
            </Tooltip>
            <Tooltip content={record.status === 'ACTIVE' ? t('marketing:zaloAds.campaigns.pause', 'Tạm dừng') : t('marketing:zaloAds.campaigns.resume', 'Tiếp tục')}>
              <IconCard
                icon={record.status === 'ACTIVE' ? 'pause' : 'play'}
                variant={record.status === 'ACTIVE' ? 'secondary' : 'primary'}
                size="sm"
                onClick={() => {
                  console.log('Toggle campaign:', record.id);
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleEditCampaign]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('marketing:zaloAds.campaigns.status.active', 'Đang chạy'),
        icon: 'play',
        value: 'ACTIVE',
      },
      {
        id: 'paused',
        label: t('marketing:zaloAds.campaigns.status.paused', 'Tạm dừng'),
        icon: 'pause',
        value: 'PAUSED',
      },
      {
        id: 'draft',
        label: t('marketing:zaloAds.campaigns.status.draft', 'Bản nháp'),
        icon: 'edit',
        value: 'DRAFT',
      },
      {
        id: 'pending',
        label: t('marketing:zaloAds.campaigns.status.pending', 'Chờ duyệt'),
        icon: 'clock',
        value: 'PENDING_REVIEW',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): ZaloAdsCampaignQueryDto => {
      const queryParams: ZaloAdsCampaignQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as ZaloAdsCampaignStatus;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ZaloAdsCampaignDto, ZaloAdsCampaignQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // API call với query từ dataTable
  const { data: campaignsData, isLoading } = useZaloAdsCampaigns(dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng với API data
  useEffect(() => {
    updateTableDataRef.current(campaignsData, isLoading);
  }, [campaignsData, isLoading]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        ACTIVE: t('marketing:zaloAds.campaigns.status.active', 'Đang chạy'),
        PAUSED: t('marketing:zaloAds.campaigns.status.paused', 'Tạm dừng'),
        DRAFT: t('marketing:zaloAds.campaigns.status.draft', 'Bản nháp'),
        PENDING_REVIEW: t('marketing:zaloAds.campaigns.status.pending', 'Chờ duyệt'),
        REJECTED: t('marketing:zaloAds.campaigns.status.rejected', 'Từ chối'),
      },
      t,
    });

  const handleCreateSuccess = () => {
    hideCreateForm();
    setSearchParams({});
  };

  const handleEditSuccess = () => {
    hideEditForm();
    setSelectedCampaign(null);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:zaloAds.campaigns.title', 'Zalo Ads Campaigns')}
        description={t('marketing:zaloAds.campaigns.description', 'Quản lý chiến dịch quảng cáo Zalo')}
        actions={
          <Button onClick={() => showCreateForm()} className="gap-2">
            <Plus className="h-4 w-4" />
            {t('marketing:zaloAds.campaigns.createCampaign', 'Tạo chiến dịch')}
          </Button>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.campaigns.stats.totalCampaigns', 'Tổng chiến dịch')}
            </span>
            <Target className="h-4 w-4 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-blue-600">
            {campaignsData?.meta.totalItems || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.campaigns.stats.allTime', 'Tất cả thời gian')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.campaigns.stats.activeCampaigns', 'Đang chạy')}
            </span>
            <Play className="h-4 w-4 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600">
            {campaignsData?.items.filter((campaign: ZaloAdsCampaignDto) => campaign.status === 'ACTIVE').length || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.campaigns.stats.currentlyRunning', 'Hiện đang chạy')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.campaigns.stats.totalSpend', 'Tổng chi phí')}
            </span>
            <DollarSign className="h-4 w-4 text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600">
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND',
              maximumFractionDigits: 0
            }).format(
              campaignsData?.items.reduce((sum: number, campaign: ZaloAdsCampaignDto) =>
                sum + (campaign.dailyBudget || campaign.lifetimeBudget || 0), 0) || 0
            )}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.campaigns.stats.totalBudget', 'Tổng ngân sách')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.campaigns.stats.performance', 'Hiệu suất')}
            </span>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-purple-600">
            2.4x
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.campaigns.stats.averageRoas', 'ROAS trung bình')}
          </p>
        </Card>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showCreateForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Campaigns Table */}
      <Card className="overflow-hidden">
        <Table<ZaloAdsCampaignDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={campaignsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: campaignsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: campaignsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* SlideInForm cho tạo chiến dịch */}
      <SlideInForm isVisible={isCreateVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zaloAds.campaigns.create.title', 'Tạo chiến dịch Zalo Ads')}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {t('marketing:zaloAds.campaigns.create.description', 'Tạo chiến dịch quảng cáo mới trên Zalo')}
            </p>
          </div>
          <CreateZaloAdsCampaignForm onSuccess={handleCreateSuccess} onCancel={hideCreateForm} />
        </div>
      </SlideInForm>

      {/* SlideInForm cho chỉnh sửa chiến dịch */}
      <SlideInForm isVisible={isEditVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zaloAds.campaigns.edit.title', 'Chỉnh sửa chiến dịch')}
            </h3>
            {selectedCampaign && (
              <p className="text-sm text-muted-foreground mt-1">
                Chiến dịch: {selectedCampaign.name}
              </p>
            )}
          </div>
          {selectedCampaign && (
            <CreateZaloAdsCampaignForm
              campaign={selectedCampaign}
              onSuccess={handleEditSuccess}
              onCancel={hideEditForm}
            />
          )}
        </div>
      </SlideInForm>
    </div>
  );
}

export default ZaloAdsCampaignsPage;
