import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import TagForm from '../components/forms/TagForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { Tag, TagStatus, CreateTagRequest, TagQueryParams } from '../types/tag.types';
import { useTags, useCreateTag, useDeleteTag } from '../hooks';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

/**
 * <PERSON>rang quản lý tag sử dụng các hooks tối ưu
 */
const TagManagementPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // No need for selectedTag state in this implementation

  // Sử dụng hooks từ API
  const { data: tagData, isLoading } = useTags({} as TagQueryParams);
  const createTagMutation = useCreateTag();
  const deleteTagMutation = useDeleteTag();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback((id: number | string) => {
    console.log('Edit tag', id);
    // Thực hiện logic chỉnh sửa
  }, []);

  // Xử lý xóa
  const handleDelete = useCallback(
    (id: number | string) => {
      if (typeof id === 'string') {
        id = parseInt(id, 10);
      }
      if (!isNaN(id)) {
        deleteTagMutation.mutate(id);
      }
    },
    [deleteTagMutation]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<Tag>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '10%', sortable: true },
      { key: 'name', title: 'Tên tag', dataIndex: 'name', width: '20%', sortable: true },
      {
        key: 'description',
        title: 'Mô tả',
        dataIndex: 'description',
        width: '30%',
        sortable: true,
      },
      {
        key: 'status',
        title: 'Trạng thái',
        dataIndex: 'status',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as TagStatus;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                status === TagStatus.ACTIVE
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              }`}
            >
              {status === TagStatus.ACTIVE ? t('common:active') : t('common:inactive')}
            </div>
          );
        },
      },
      { key: 'createdAt', title: 'Ngày tạo', dataIndex: 'createdAt', width: '15%', sortable: true },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '10%',
        render: (_: unknown, record: Tag) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common:edit')} position="top">
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => handleEdit(record.id)}
              />
            </Tooltip>
            <Tooltip content={t('common:delete')} position="top">
              <IconCard
                icon="trash"
                variant="default"
                size="sm"
                onClick={() => handleDelete(record.id)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleEdit, handleDelete]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: TagStatus.ACTIVE },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: TagStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): TagQueryParams => {
    const queryParams: TagQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as TagStatus;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<Tag, TagQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi values thành CreateTagRequest
    const tagData: CreateTagRequest = {
      name: values.name as string,
      description: values.description as string | undefined,
      color: values.color as string | undefined,
      status: values.status as TagStatus | undefined,
    };

    createTagMutation.mutate(tagData);
    hideAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [TagStatus.ACTIVE]: t('common:active'),
      [TagStatus.INACTIVE]: t('common:inactive'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <TagForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={tagData || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: dataTable.tableData.currentPage,
            pageSize: dataTable.tableData.pageSize,
            total: tagData ? tagData.length : 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default TagManagementPage;
