import { BankAccount, BankAccountsParams, BankAccountsResponse } from '../types/account';

/**
 * Mock data for bank accounts
 */
const mockBankAccounts: BankAccount[] = [
  {
    id: '1',
    bankCode: 'MB',
    bankName: 'MB Bank',
    accountName: 'PHAM THI HUONG GIANG',
    accountNumber: '0348522****',
    logoUrl: '/src/modules/integration/assets/mb-logo.png',
    linkedDate: '2023-05-15T10:30:00Z',
    isDefault: true,
  },
  {
    id: '2',
    bankCode: 'MB',
    bankName: 'MB Bank',
    accountName: 'PHAM THI HUONG GIANG',
    accountNumber: '0348522****',
    logoUrl: '/src/modules/integration/assets/mb-logo.png',
    linkedDate: '2023-06-20T14:45:00Z',
    isDefault: false,
  },
  {
    id: '3',
    bankCode: 'MB',
    bankName: 'MB Bank',
    accountName: 'PHAM THI HUONG GIANG',
    accountNumber: '0348522****',
    logoUrl: '/src/modules/integration/assets/mb-logo.png',
    linkedDate: '2023-07-10T09:15:00Z',
    isDefault: false,
  },
];

/**
 * Service for bank account operations
 */
const AccountService = {
  /**
   * Get list of linked bank accounts
   * @param params Optional parameters for filtering
   * @returns Promise with bank accounts response
   */
  getLinkedAccounts: async (params?: BankAccountsParams): Promise<BankAccountsResponse> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Filter accounts if params are provided
    let filteredAccounts = [...mockBankAccounts];

    if (params?.bankCode) {
      filteredAccounts = filteredAccounts.filter(account => account.bankCode === params.bankCode);
    }

    // Return mock response
    return {
      code: 200,
      message: 'Success',
      result: filteredAccounts,
    };
  },

  /**
   * Add a new linked bank account
   * @param account Bank account data
   * @returns Promise with the added account
   */
  addLinkedAccount: async (
    account: Omit<BankAccount, 'id' | 'linkedDate'>
  ): Promise<BankAccountsResponse> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Create new account with generated ID and current date
    const newAccount: BankAccount = {
      ...account,
      id: Math.random().toString(36).substring(2, 11),
      linkedDate: new Date().toISOString(),
    };

    // In a real implementation, this would call an API
    // For mock purposes, we just return the new account
    return {
      code: 200,
      message: 'Account linked successfully',
      result: [newAccount],
    };
  },

  /**
   * Remove a linked bank account
   * @param accountId ID of the account to remove
   * @returns Promise with success response
   */
  removeLinkedAccount: async (_accountId: string): Promise<BankAccountsResponse> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // In a real implementation, this would call an API to remove the account with the given ID
    // For mock purposes, we just return success
    console.log(`Removing account with ID: ${_accountId}`); // Sử dụng tham số để tránh lỗi unused
    return {
      code: 200,
      message: 'Account unlinked successfully',
      result: [],
    };
  },
};

export default AccountService;
