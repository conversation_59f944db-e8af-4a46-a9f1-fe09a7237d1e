import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { ComponentDemo } from '@/modules/components/components';

const TypographyPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.typography.title', 'Typography')}
        </h1>
        <p className="text-muted">
          {t(
            'components.typography.description',
            'Typography components for consistent text styling across your application.'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.typography.headings.title', 'Headings')}
        description={t(
          'components.typography.headings.description',
          'Heading variants from h1 to h6.'
        )}
        code={`import { Typography } from '@/shared/components/common';

<Typography variant="h1">Heading 1</Typography>
<Typography variant="h2">Heading 2</Typography>
<Typography variant="h3">Heading 3</Typography>
<Typography variant="h4">Heading 4</Typography>
<Typography variant="h5">Heading 5</Typography>
<Typography variant="h6">Heading 6</Typography>`}
      >
        <div className="space-y-4">
          <Typography variant="h1">Heading 1</Typography>
          <Typography variant="h2">Heading 2</Typography>
          <Typography variant="h3">Heading 3</Typography>
          <Typography variant="h4">Heading 4</Typography>
          <Typography variant="h5">Heading 5</Typography>
          <Typography variant="h6">Heading 6</Typography>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.typography.body.title', 'Body Text')}
        description={t(
          'components.typography.body.description',
          'Body text variants for paragraphs and general content.'
        )}
        code={`import { Typography } from '@/shared/components/common';

<Typography variant="body1">
  Body 1 - Regular paragraph text with standard size. This is the default variant.
</Typography>

<Typography variant="body2">
  Body 2 - Smaller paragraph text for less important content.
</Typography>

<Typography variant="subtitle1">
  Subtitle 1 - Larger subtitle text.
</Typography>

<Typography variant="subtitle2">
  Subtitle 2 - Smaller subtitle text.
</Typography>`}
      >
        <div className="space-y-4">
          <Typography variant="body1">
            Body 1 - Regular paragraph text with standard size. This is the default variant. Lorem
            ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies
            tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl.
          </Typography>

          <Typography variant="body2">
            Body 2 - Smaller paragraph text for less important content. Lorem ipsum dolor sit amet,
            consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl
            aliquam nisl, eget aliquam nisl nisl eget nisl.
          </Typography>

          <Typography variant="subtitle1">Subtitle 1 - Larger subtitle text.</Typography>

          <Typography variant="subtitle2">Subtitle 2 - Smaller subtitle text.</Typography>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.typography.colors.title', 'Text Colors')}
        description={t(
          'components.typography.colors.description',
          'Typography with different color options.'
        )}
        code={`import { Typography } from '@/shared/components/common';

<Typography color="default">Default Text Color</Typography>
<Typography color="primary">Primary Text Color</Typography>
<Typography color="secondary">Secondary Text Color</Typography>
<Typography color="success">Success Text Color</Typography>
<Typography color="warning">Warning Text Color</Typography>
<Typography color="danger">Danger Text Color</Typography>
<Typography color="info">Info Text Color</Typography>
<Typography color="muted">Muted Text Color</Typography>`}
      >
        <div className="space-y-2">
          <Typography color="default">Default Text Color</Typography>
          <Typography color="primary">Primary Text Color</Typography>
          <Typography color="secondary">Secondary Text Color</Typography>
          <Typography color="success">Success Text Color</Typography>
          <Typography color="warning">Warning Text Color</Typography>
          <Typography color="danger">Danger Text Color</Typography>
          <Typography color="info">Info Text Color</Typography>
          <Typography color="muted">Muted Text Color</Typography>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.typography.weights.title', 'Font Weights')}
        description={t(
          'components.typography.weights.description',
          'Typography with different font weights.'
        )}
        code={`import { Typography } from '@/shared/components/common';

<Typography weight="light">Light Text (300)</Typography>
<Typography weight="normal">Normal Text (400)</Typography>
<Typography weight="medium">Medium Text (500)</Typography>
<Typography weight="semibold">Semi Bold Text (600)</Typography>
<Typography weight="bold">Bold Text (700)</Typography>`}
      >
        <div className="space-y-2">
          <Typography weight="light">Light Text (300)</Typography>
          <Typography weight="normal">Normal Text (400)</Typography>
          <Typography weight="medium">Medium Text (500)</Typography>
          <Typography weight="semibold">Semi Bold Text (600)</Typography>
          <Typography weight="bold">Bold Text (700)</Typography>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.typography.alignment.title', 'Text Alignment')}
        description={t(
          'components.typography.alignment.description',
          'Typography with different text alignments.'
        )}
        code={`import { Typography } from '@/shared/components/common';

<Typography align="left">Left aligned text (default)</Typography>
<Typography align="center">Center aligned text</Typography>
<Typography align="right">Right aligned text</Typography>
<Typography align="justify">
  Justify aligned text. This paragraph contains more text to demonstrate
  justified alignment. The text will be stretched to fill the entire width
  of the container, creating a clean edge on both the left and right sides.
</Typography>`}
      >
        <div className="space-y-4">
          <Typography align="left">Left aligned text (default)</Typography>
          <Typography align="center">Center aligned text</Typography>
          <Typography align="right">Right aligned text</Typography>
          <Typography align="justify">
            Justify aligned text. This paragraph contains more text to demonstrate justified
            alignment. The text will be stretched to fill the entire width of the container,
            creating a clean edge on both the left and right sides. Lorem ipsum dolor sit amet,
            consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl
            aliquam nisl, eget aliquam nisl nisl eget nisl.
          </Typography>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.typography.truncation.title', 'Text Truncation')}
        description={t(
          'components.typography.truncation.description',
          'Typography with truncation for long text.'
        )}
        code={`import { Typography } from '@/shared/components/common';

// Single line truncation
<Typography truncate>
  This is a very long text that will be truncated with an ellipsis when it reaches
  the end of its container. You can see that it doesn't wrap to a new line.
</Typography>

// Multi-line truncation
<Typography truncate lines={2}>
  This text demonstrates multi-line truncation. It will show exactly 2 lines
  and then truncate with an ellipsis. This is useful for card descriptions,
  article summaries, and other places where you want to show a preview of longer content.
</Typography>`}
      >
        <div className="space-y-4">
          <div className="max-w-md">
            <Typography truncate>
              This is a very long text that will be truncated with an ellipsis when it reaches the
              end of its container. You can see that it doesn't wrap to a new line. Lorem ipsum
              dolor sit amet, consectetur adipiscing elit.
            </Typography>
          </div>

          <div className="max-w-md">
            <Typography truncate lines={2}>
              This text demonstrates multi-line truncation. It will show exactly 2 lines and then
              truncate with an ellipsis. This is useful for card descriptions, article summaries,
              and other places where you want to show a preview of longer content without taking up
              too much space in the UI. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl.
            </Typography>
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.typography.responsive.title', 'Responsive Typography')}
        description={t(
          'components.typography.responsive.description',
          'Typography with responsive font sizes.'
        )}
        code={`import { Typography } from '@/shared/components/common';

<Typography
  fontSize={{
    base: '16px',
    md: '18px',
    lg: '20px'
  }}
>
  This text has responsive font size that changes based on screen size.
</Typography>`}
      >
        <div className="space-y-4">
          <Typography
            fontSize={{
              base: '16px',
              md: '18px',
              lg: '20px',
            }}
          >
            This text has responsive font size that changes based on screen size. Resize your
            browser window to see the effect.
          </Typography>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default TypographyPage;
