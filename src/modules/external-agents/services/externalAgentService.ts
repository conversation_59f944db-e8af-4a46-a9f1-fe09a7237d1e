import { externalAgentApi } from '../api';
import {
  ExternalAgent,
  ExternalAgentCreateDto,
  ExternalAgentUpdateDto,
  ExternalAgentQueryDto,
  ExternalAgentListResponse,
  ConnectionTestResult,
  AgentPerformanceMetrics,
} from '../types';
import { DEFAULT_QUERY_PARAMS, DEFAULT_TIMEOUT } from '../constants';

export const externalAgentService = {
  // Get external agents with business logic
  getExternalAgents: async (params?: ExternalAgentQueryDto): Promise<ExternalAgentListResponse> => {
    const queryParams = {
      ...DEFAULT_QUERY_PARAMS,
      ...params,
    };

    // Validate limit
    if (queryParams.limit && queryParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    // Validate page
    if (queryParams.page && queryParams.page < 1) {
      queryParams.page = 1;
    }

    return externalAgentApi.getExternalAgents(queryParams);
  },

  // Get external agent with validation
  getExternalAgent: async (id: string): Promise<ExternalAgent> => {
    if (!id || id.trim() === '') {
      throw new Error('Agent ID is required');
    }

    return externalAgentApi.getExternalAgent(id);
  },

  // Create external agent with validation
  createExternalAgent: async (data: ExternalAgentCreateDto): Promise<ExternalAgent> => {
    // Validate required fields
    if (!data.name || data.name.trim() === '') {
      throw new Error('Agent name is required');
    }

    if (!data.endpoint || data.endpoint.trim() === '') {
      throw new Error('Agent endpoint is required');
    }

    // Validate endpoint URL
    try {
      new URL(data.endpoint);
    } catch {
      throw new Error('Invalid endpoint URL');
    }

    // Set default capabilities if not provided
    const agentData = {
      ...data,
      capabilities: data.capabilities || [],
      metadata: data.metadata || {},
      tags: data.tags || [],
    };

    return externalAgentApi.createExternalAgent(agentData);
  },

  // Update external agent with validation
  updateExternalAgent: async (id: string, data: ExternalAgentUpdateDto): Promise<ExternalAgent> => {
    if (!id || id.trim() === '') {
      throw new Error('Agent ID is required');
    }

    // Validate endpoint URL if provided
    if (data.endpoint) {
      try {
        new URL(data.endpoint);
      } catch {
        throw new Error('Invalid endpoint URL');
      }
    }

    return externalAgentApi.updateExternalAgent(id, data);
  },

  // Delete external agent with confirmation
  deleteExternalAgent: async (id: string): Promise<void> => {
    if (!id || id.trim() === '') {
      throw new Error('Agent ID is required');
    }

    return externalAgentApi.deleteExternalAgent(id);
  },

  // Test connection with timeout
  testConnection: async (id: string, timeout = DEFAULT_TIMEOUT): Promise<ConnectionTestResult> => {
    if (!id || id.trim() === '') {
      throw new Error('Agent ID is required');
    }

    const startTime = Date.now();
    
    try {
      const result = await Promise.race([
        externalAgentApi.testConnection(id),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Connection test timeout')), timeout)
        ),
      ]);

      return {
        ...result,
        responseTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime,
        timestamp: new Date().toISOString(),
      };
    }
  },

  // Get performance metrics with caching
  getPerformanceMetrics: async (id: string): Promise<AgentPerformanceMetrics> => {
    if (!id || id.trim() === '') {
      throw new Error('Agent ID is required');
    }

    return externalAgentApi.getPerformanceMetrics(id);
  },

  // Bulk operations with validation
  bulkUpdateStatus: async (ids: string[], status: string): Promise<void> => {
    if (!ids || ids.length === 0) {
      throw new Error('At least one agent ID is required');
    }

    if (ids.length > 50) {
      throw new Error('Cannot update more than 50 agents at once');
    }

    return externalAgentApi.bulkUpdateStatus(ids, status);
  },

  bulkDelete: async (ids: string[]): Promise<void> => {
    if (!ids || ids.length === 0) {
      throw new Error('At least one agent ID is required');
    }

    if (ids.length > 20) {
      throw new Error('Cannot delete more than 20 agents at once');
    }

    return externalAgentApi.bulkDelete(ids);
  },
};
