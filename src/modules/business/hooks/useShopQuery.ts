import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';
import { ShopService } from '../services/shop.service';
import { CreateOrUpdateShopInfoDto } from '../types/shop.types';

/**
 * Query keys cho shop
 */
export const SHOP_QUERY_KEYS = {
  all: ['shop'] as const,
  list: () => [...SHOP_QUERY_KEYS.all, 'list'] as const,
  info: () => [...SHOP_QUERY_KEYS.all, 'info'] as const,
};

/**
 * Hook để lấy danh sách cửa hàng
 */
export const useShopList = () => {
  const { t } = useTranslation('business');

  return useQuery({
    queryKey: SHOP_QUERY_KEYS.list(),
    queryFn: async () => {
      const response = await ShopService.getShopList();
      console.log('🔍 [useShopList] Full API response:', response);
      console.log('🔍 [useShopList] Response.data:', response.data);
      console.log('🔍 [useShopList] Response.result:', response.result);
      // Try both data and result to see which one works
      return response.data || response.result || [];
    },
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (chưa có cửa hàng), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
    meta: {
      errorMessage: t('business:shop.messages.loadError'),
    },
  });
};

/**
 * Hook để lấy thông tin cửa hàng đơn lẻ (giữ lại để tương thích)
 */
export const useShopInfo = () => {

  return useQuery({
    queryKey: SHOP_QUERY_KEYS.info(),
    queryFn: async () => {
      const response = await ShopService.getShopInfo();
      console.log('🔍 [useShopInfo] Full API response:', response);
      console.log('🔍 [useShopInfo] Response.data:', response.data);
      console.log('🔍 [useShopInfo] Response.result:', response.result);
      // Try both data and result to see which one works
      return response.data || response.result;
    },
    retry: (failureCount, error: Error & { response?: { status: number } }) => {
      // Nếu lỗi 404 (chưa có thông tin cửa hàng), không retry
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

/**
 * Hook để tạo thông tin cửa hàng
 */
export const useCreateShopInfo = () => {
  const { t } = useTranslation('business');
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrUpdateShopInfoDto) => ShopService.createShopInfo(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:shop.messages.createSuccess'),
      });
      // Invalidate và refetch shop list và info
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.list() });
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.info() });
    },
    onError: (error: Error) => {
      console.error('Create shop info error:', error);
      NotificationUtil.error({
        message: t('business:shop.messages.createError'),
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin cửa hàng
 */
export const useUpdateShopInfo = () => {
  const { t } = useTranslation('business');
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrUpdateShopInfoDto) => ShopService.updateShopInfo(data),
    onSuccess: () => {
      NotificationUtil.success({
        message: t('business:shop.messages.updateSuccess'),
      });
      // Invalidate và refetch shop list và info
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.list() });
      queryClient.invalidateQueries({ queryKey: SHOP_QUERY_KEYS.info() });
    },
    onError: (error: Error) => {
      console.error('Update shop info error:', error);
      NotificationUtil.error({
        message: t('business:shop.messages.updateError'),
      });
    },
  });
};
