# Sử dụng Node.js 22.14.0 làm base image
FROM node:22.14.0-alpine

# Thiết lập thư mục làm việc
WORKDIR /app

# Thiết lập biến môi trường
ENV NODE_ENV=development
ENV NODE_OPTIONS=--max-old-space-size=4096
ENV VITE_PORT=5175

# Sao chép package.json và package-lock.json
COPY package*.json ./

# Cài đặt dependencies
RUN npm install --legacy-peer-deps

# Sao chép toàn bộ mã nguồn
COPY . .

# Mở cổng 5175 cho Vite development server
EXPOSE 5175

# Khởi động Vite development server vớ<PERSON> tù<PERSON> chọn --host và cổng 5175
CMD ["sh", "-c", "npx vite --host --port 5175"]
